defmodule QuoteX.Accounts.Reservation do
  use Ash.Resource,
    otp_app: :quotex,
    domain: QuoteX.Accounts,
    extensions: [AshJsonApiWrapper.Resource, AshGraphql.Resource],
    data_layer: AshJsonApiWrapper.DataLayer

  json_api_wrapper do
    base_entity_path "https://openlibrary.org"

    endpoints [
      get("/search.json")
    ]
    fields [:author_name, :title, :type, :id]
    actions [:read]
  end

  attributes do
    attribute :id, :string, primary_key?: true, allow_nil?: false, public?: true
    attribute :author_name, :string, public?: true
    attribute :title, :string, public?: true
    attribute :type, :string, public?: true
  end

  graphql do
    type :reservation
    derive_sort? false
  end

  actions do
    defaults [:read]
    default_accept [:author_name, :title, :type]
  end
end
