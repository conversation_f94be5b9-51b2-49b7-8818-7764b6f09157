defmodule QuoteX.Accounts.Reservation do
  use Ash.Resource,
    otp_app: :quotex,
    domain: QuoteX.Accounts,
    extensions: [AshJsonApiWrapper.Resource, AshGraphql.Resource],
    data_layer: AshJsonApiWrapper.DataLayer

  json_api_wrapper do
    base_entity_path "https://openlibrary.org"

    endpoints [
      get("/search.json")
    ]
    fields [:author_name, :title, :type, :id]
    actions [:read]
  end

  attributes do
    attribute :id, :string, primary_key?: true, allow_nil?: false
    attribute :author_name, :string
    attribute :title, :string
    attribute :type, :string
  end

  graphql do
    type :reservation

    queries do
      get :get_reservation, :read
      list :list_reservations, :read
    end
  end

  actions do
    defaults [:read]
    default_accept [:author_name, :title, :type]
  end
end
